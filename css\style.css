@charset "utf-8";


* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

header {
    background-color: #1a237e; 
    width: 100%;
    margin:0;
    padding: 10px 0;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 1);
    position: fixed;
}

nav {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

nav h1 {
    color: white;
    margin: 0 0 10px 0;
    font-size: 2em;
    align-self: center;
}

nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    gap: 10px;
}

nav ul li {
    margin: 0;
}

nav ul li a {
    display: block;
    padding: 10px 20px;
    background-color: #3f51b5; 
    color: white;
    text-decoration: none;
    border-radius: 8px; 
    transition: background-color 0.3s ease;
}

nav ul li a:hover {
    background-color: #d32f2f;
}

#cero {
    padding-top: 110px;
}

#cero p {
    color: #1a237e;
    font-size: 2.5em;
    font-weight: bold;
    text-align: center;
    background-image: url('../imagenes/imagen1.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    padding: 40px;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin: 0;
}

.uno, .dos {
    display: inline-block;
    width: 50%;
    vertical-align: top;
    min-height: 200px;
    box-sizing: border-box;
}

.uno {
    float: left;
}

.dos {
    float: right;
    margin-right: 0;
}

#content::after {
    content: "";
    display: table;
    clear: both;
}

.uno {
    background-color: #64b5f6;
    padding: 20px;
    text-align: center;
    color: #333;
}

.uno h3 {
    font-weight: bold;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 2px solid white;
}

.uno p {
    margin: 0;
}

.dos {
    background-color: #d32f2f; 
    padding: 20px;
    text-align: center;
    color: white;
    position: relative;
}

.dos h3 {
    font-weight: bold;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 2px solid white;
}

.dos p {
    margin: 5px 0;
}

.dos p.autor {
    position: absolute;
    bottom: 10px;
    right: 15px;
    font-size: 0.8em;
    text-align: right;
    margin: 0;
}

.tres {
    background-color: #ffeb3b; 
    width: 100%;
    margin: 20px 0;
    padding: 40px;
    text-align: center;
}

.tres p {
    margin-top: 10px;
    color: #333;
    font-size: 2em;
}

.cinco {
    background-color: #64b5f6;
    padding: 20px;
    text-align: center;
    color: #333;
    position: relative;
    margin: 0 0 20px 0;
    border-radius: 10px;
}

.cinco h3 {
    font-weight: bold;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 2px solid white; 
}

.cinco p {
    margin: 0;
}

.cuatro {
    background-color: lightblue !important; 
    border-radius: 15px; 
    padding: 20px !important;
    margin: 15px 20px 15px 20px !important;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 1);
    float: left !important;
    width: 45% !important;
    min-height: 120px !important;
    display: block !important;
    position: relative;
    z-index: 1;
}

.cuatro .dentroDeCuatro {
    color: black !important;
    font-size: 1em !important;
    text-align: center !important;
}

.cuatro .dentroDeCuatro p {
    color: black !important;
    font-size: 1em !important;
    margin: 0 !important;
    text-align: center !important;
}

.cinco::after {
    content: "";
    display: table;
    clear: both;
}
